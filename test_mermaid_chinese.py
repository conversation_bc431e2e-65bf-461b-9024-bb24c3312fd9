#!/usr/bin/env python3
"""
测试Mermaid中文文本优化效果
"""

def test_mermaid_chinese_formatting():
    """测试Mermaid中文文本格式化"""
    
    print("🧪 测试Mermaid中文文本格式化...")
    
    # 测试用例1：优化前的长文本节点
    old_format = """
graph TD
    A[刘定富非法经营者52岁取保候审获利25000元] -->|购买柴油| B[陌生男子身份不明]
    A -->|销售柴油| C[万海峰大货车司机]
    A -->|非法经营| D[普宁市占陇镇朴兜村停车场]
    D -->|查获| E[储油罐4个加油枪泵1台柴油13635kg价值114,602.18元]
"""
    
    # 测试用例2：优化后的格式化文本节点
    new_format = """
graph TD
    A[刘定富\\n非法经营\\n52岁\\n取保候审\\n获利2.5万元] -->|购买柴油| B[陌生男子\\n身份不明]
    A -->|销售柴油| C[万海峰\\n大货车司机]
    A -->|非法经营| D[普宁市占陇镇\\n朴兜村停车场]
    D -->|查获| E[储油罐4个\\n加油枪泵1台\\n柴油13.6吨\\n价值11.5万元]
    
    style A fill:#FFB6C1,stroke:#333
    style B fill:#87CEEB,stroke:#333
    style C fill:#87CEEB,stroke:#333
    style D fill:#FFD700,stroke:#333
    style E fill:#98FB98,stroke:#333
    
    classDef 主犯 fill:#FFB6C1,stroke:#333;
    classDef 从犯 fill:#87CEEB,stroke:#333;
    classDef 场所 fill:#FFD700,stroke:#333;
    classDef 物品 fill:#98FB98,stroke:#333;
"""
    
    print("📊 优化前的Mermaid代码:")
    print("=" * 50)
    print(old_format.strip())
    
    print("\n📈 优化后的Mermaid代码:")
    print("=" * 50)
    print(new_format.strip())
    
    # 分析改进点
    print("\n✅ 优化改进点:")
    print("1. 节点文本换行：每行不超过8个中文字符")
    print("2. 金额简化：25000元 → 2.5万元，114,602.18元 → 11.5万元")
    print("3. 数量简化：13635kg → 13.6吨")
    print("4. 样式分类：不同类型节点使用不同颜色")
    print("5. 类定义：添加classDef便于统一样式管理")
    
    # 测试另一个案例
    print("\n🔍 测试案例2：走私香烟案")
    print("=" * 50)
    
    cigarette_case = """
graph TD
    A[不明上家\\n在逃] -->|雇佣| B[罗添文\\n运输司机\\n29岁\\n羁押]
    B -->|驾驶| C[桂E86181\\n宝骏汽车]
    C -->|运输| D[南京炫赫门\\n2736条\\n价值49.2万元]
    D -->|查获于| E[沈海高速\\n铁铺路段\\n2024/5/9]
    
    style A fill:#87CEEB,stroke:#333
    style B fill:#FFB6C1,stroke:#333
    style C fill:#DDA0DD,stroke:#333
    style D fill:#98FB98,stroke:#333
    style E fill:#FFD700,stroke:#333
    
    classDef 主犯 fill:#FFB6C1,stroke:#333;
    classDef 从犯 fill:#87CEEB,stroke:#333;
    classDef 工具 fill:#DDA0DD,stroke:#333;
    classDef 物品 fill:#98FB98,stroke:#333;
    classDef 场所 fill:#FFD700,stroke:#333;
"""
    
    print(cigarette_case.strip())
    
    print("\n🎯 格式化规则验证:")
    print("✅ 姓名行：罗添文 (3个字符)")
    print("✅ 角色行：运输司机 (4个字符)")
    print("✅ 状态行：29岁、羁押 (各3个字符)")
    print("✅ 物品行：南京炫赫门 (5个字符)")
    print("✅ 数量行：2736条 (5个字符)")
    print("✅ 金额行：价值49.2万元 (7个字符)")
    print("✅ 地点行：沈海高速 (4个字符)")
    print("✅ 时间行：2024/5/9 (8个字符)")

def analyze_text_length(text: str) -> dict:
    """分析文本长度"""
    lines = text.split('\\n')
    analysis = {
        'total_lines': len(lines),
        'line_lengths': [],
        'max_length': 0,
        'avg_length': 0
    }
    
    for line in lines:
        length = len(line)
        analysis['line_lengths'].append(length)
        if length > analysis['max_length']:
            analysis['max_length'] = length
    
    if analysis['line_lengths']:
        analysis['avg_length'] = sum(analysis['line_lengths']) / len(analysis['line_lengths'])
    
    return analysis

def test_text_length_analysis():
    """测试文本长度分析"""
    print("\n📏 文本长度分析测试:")
    print("=" * 50)
    
    test_cases = [
        "刘定富\\n非法经营\\n52岁\\n取保候审\\n获利2.5万元",
        "罗添文\\n运输司机\\n29岁\\n羁押",
        "南京炫赫门\\n2736条\\n价值49.2万元",
        "沈海高速\\n铁铺路段\\n2024/5/9"
    ]
    
    for i, case in enumerate(test_cases, 1):
        analysis = analyze_text_length(case)
        print(f"案例{i}: {case.replace('\\n', ' | ')}")
        print(f"  行数: {analysis['total_lines']}")
        print(f"  各行长度: {analysis['line_lengths']}")
        print(f"  最大长度: {analysis['max_length']} 字符")
        print(f"  平均长度: {analysis['avg_length']:.1f} 字符")
        
        # 检查是否符合规范
        if analysis['max_length'] <= 8:
            print("  ✅ 符合长度规范 (≤8字符)")
        else:
            print("  ❌ 超出长度规范 (>8字符)")
        print()

if __name__ == "__main__":
    test_mermaid_chinese_formatting()
    test_text_length_analysis()
    print("\n🎉 Mermaid中文文本优化测试完成!")
