# 案件人物关系图功能完成报告

## 🎯 功能需求

根据您的要求，新增了"案件人物关系图"用户输入框，允许用户自定义关系图生成需求，并修改了分析需求模板。

## ✅ 已完成的修改

### 1. 后端修改 - multi_agents.py

#### 新增全局变量
```python
# 全局用户需求变量
self.user_requirements = None
self.user_requirements_new = None
self.user_requirements_count = 0
self.user_relationship_images = None  # 新增
```

#### 修改 `_set_user_requirements` 方法
```python
def _set_user_requirements(self, user_requirements: str = None, user_relationship_images: str = None):
    """设置用户需求并处理相关变量"""
    # ... 原有逻辑 ...
    
    # 处理关系图需求
    if user_relationship_images:
        self.user_relationship_images = user_relationship_images
    else:
        # 默认关系图需求
        self.user_relationship_images = """梳理犯罪嫌疑人之间以及组织或公司之间的各种多层级复杂关系（横向关系，纵向关系），犯罪嫌疑人的信息需要丰富一些，例如角色，年龄、强制措施，前科等关键信息（时间，罪名，强制措施），有明确关系的在线条上标注，生成Mermaid格式的关系图代码。
关系图要清晰易读，不要重复画线，Mermaid代码要正确 
地点分析清楚（例如：窝点，交货点，送货点，接货点）
什么货品，货品金额（如果有货品金额直接连接货品）
最好案件链条关系至上而下。
没有明确关系的不要强行连接，没有明确说明层级关系的不要硬改层级。
信息提取要严谨，案件内容中没有提及的不要提，案件内容中提及的不要漏。

注意Mermaid代码换行语法要正确，换行不要使用\\n格式，可参考示例：
A[张某某-组织者
35岁-另案件起诉]"""
```

#### 修改分析需求模板
```python
# 动态生成分析需求
analysis_requirements = f"""任务目标：
1. 组织架构和供应链网络解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑、供应链上下游）
2. 结构化数据提取（CSV格式，{self.user_requirements_count}列严格校验）
3. 所有层级关系和供应链网络图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{self.user_requirements_new}

1."年龄"要素提取要求：
其中"年龄" 要素，优先从案件内容中分析获取，若无法直接获得"年龄"要素，则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"
2."姓名/公司/代号/昵称"要素提取要求：如果是昵称尽量获取是什么软件的昵称。 

3.输出CSV格式（{self.user_requirements_count}列）：
{self.user_requirements_new}

第三步：关系图谱
{self.user_relationship_images}
"""
```

#### 修改方法签名
- `_initialize_agent(user_requirements, user_relationship_images)`
- `extract_multiple_cases(..., user_relationship_images, ...)`
- `process_multi_case_file(..., user_relationship_images)`

### 2. 前端修改 - streamlit_app.py 和 streamlit_app - 副本.py

#### 新增关系图需求输入框
```python
# 案件人物关系图需求定义
st.markdown("### 🖼️ 案件人物关系图")
user_relationship_images = st.text_area(
    "关系图需求配置",
    value="""梳理犯罪嫌疑人之间以及组织或公司之间的各种多层级复杂关系（横向关系，纵向关系），犯罪嫌疑人的信息需要丰富一些，例如角色，年龄、强制措施，前科等关键信息（时间，罪名，强制措施），有明确关系的在线条上标注，生成Mermaid格式的关系图代码。
关系图要清晰易读，不要重复画线，Mermaid代码要正确 
地点分析清楚（例如：窝点，交货点，送货点，接货点）
什么货品，货品金额（如果有货品金额直接连接货品）
最好案件链条关系至上而下。
没有明确关系的不要强行连接，没有明确说明层级关系的不要硬改层级。
信息提取要严谨，案件内容中没有提及的不要提，案件内容中提及的不要漏。

注意Mermaid代码换行语法要正确，换行不要使用\\n格式，可参考示例：
A[张某某-组织者
35岁-另案件起诉]""",
    height=200,
    help="请输入关系图生成的具体需求",
    label_visibility="collapsed"
)
```

#### 修改函数返回值
```python
# 修改前
return uploaded_file, user_requirements

# 修改后
return uploaded_file, user_requirements, user_relationship_images
```

#### 修改主函数调用
```python
# 修改前
uploaded_file, user_requirements = display_file_upload_section()

# 修改后
uploaded_file, user_requirements, user_relationship_images = display_file_upload_section()
```

#### 修改后端调用
```python
result = asyncio.run(st.session_state.orchestrator.process_multi_case_file(
    saved_file_path,
    st.session_state.current_session_id,
    user_requirements,
    st.session_state.max_concurrent,
    progress_callback,
    user_relationship_images  # 新增参数
))
```

## 🎨 界面设计

### 前端界面结构
```
### 📋 分析需求
┌─────────────────────────────────────────────────────────┐
│ 字段配置 (高度: 100px)                                  │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别, │ │
│ │ 年龄,身份证号,户籍地/现居地,文化程度,直接上级...    │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘

### 🖼️ 案件人物关系图
┌─────────────────────────────────────────────────────────┐
│ 关系图需求配置 (高度: 200px)                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 梳理犯罪嫌疑人之间以及组织或公司之间的各种多层级复杂│ │
│ │ 关系（横向关系，纵向关系），犯罪嫌疑人的信息需要丰富│ │
│ │ 一些，例如角色，年龄、强制措施，前科等关键信息...  │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 设计特点
- ✅ **独立配置区域**：字段配置和关系图配置分开
- ✅ **合适的高度**：字段配置100px，关系图配置200px
- ✅ **详细的默认值**：提供完整的配置模板
- ✅ **用户友好**：有帮助提示，标签隐藏

## 🔄 数据流

### 完整数据传递链路
```
前端用户输入
    ↓
display_file_upload_section()
    ↓ (uploaded_file, user_requirements, user_relationship_images)
main()
    ↓
orchestrator.process_multi_case_file(..., user_relationship_images)
    ↓
batch_extractor.extract_multiple_cases(..., user_relationship_images)
    ↓
_initialize_agent(user_requirements, user_relationship_images)
    ↓
_set_user_requirements(user_requirements, user_relationship_images)
    ↓
self.user_relationship_images = user_relationship_images
    ↓
_get_system_message() → analysis_requirements
    ↓
第三步：关系图谱 {self.user_relationship_images}
```

## 📊 功能对比

### 修改前
- **字段配置**：用户可自定义提取字段
- **关系图**：固定的关系图生成逻辑
- **分析模板**：硬编码的关系图要求

### 修改后
- **字段配置**：用户可自定义提取字段（保持不变）
- **关系图配置**：用户可自定义关系图生成需求
- **分析模板**：动态使用用户配置的关系图要求

## 🎯 用户价值

### 1. 灵活性提升
- ✅ **自定义关系图**：用户可根据具体案件类型定制关系图需求
- ✅ **专业配置**：支持详细的Mermaid格式和可视化要求
- ✅ **案件适配**：不同类型案件可使用不同的关系图策略

### 2. 专业性增强
- ✅ **详细要求**：支持地点分析、货品信息、层级关系等专业要求
- ✅ **格式规范**：明确的Mermaid代码格式要求和示例
- ✅ **信息严谨**：强调信息提取的准确性和完整性

### 3. 用户体验优化
- ✅ **直观界面**：清晰的功能分区和配置区域
- ✅ **默认模板**：提供完整的默认配置，降低使用门槛
- ✅ **即时生效**：配置修改立即应用到分析过程

## 🔧 技术实现

### 变量管理
- **全局一致性**：`self.user_relationship_images` 在整个类中保持一致
- **默认值处理**：提供完整的默认关系图需求模板
- **参数传递**：完整的参数传递链路确保配置正确传递

### 模板动态化
- **动态替换**：分析需求模板中的关系图部分使用用户配置
- **保持结构**：保持原有的三步分析结构
- **向后兼容**：不影响现有的字段配置功能

### 前端集成
- **无缝集成**：新功能与现有界面完美融合
- **数据同步**：前后端数据传递完整可靠
- **错误处理**：保持原有的错误处理机制

## 🎉 总结

### 完成的功能
1. ✅ **新增全局变量**：`self.user_relationship_images`
2. ✅ **前端输入框**：专门的关系图需求配置区域
3. ✅ **分析模板更新**：使用动态的关系图需求
4. ✅ **完整数据流**：从前端到后端的完整参数传递
5. ✅ **默认配置**：提供详细的默认关系图需求模板

### 用户体验提升
- **更灵活的配置**：用户可根据案件特点定制关系图需求
- **更专业的分析**：支持详细的关系图生成要求
- **更直观的界面**：清晰的功能分区和配置选项
- **更准确的结果**：用户定制的需求产生更符合期望的关系图

现在用户可以在前端界面中自定义关系图生成需求，系统会根据用户的配置生成更符合期望的案件人物关系图！🎯
