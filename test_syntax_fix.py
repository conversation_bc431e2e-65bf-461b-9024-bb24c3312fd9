#!/usr/bin/env python3
"""
测试语法修复效果
"""

import ast
import sys

def test_python_syntax(file_path):
    """测试Python文件语法"""
    print(f"🧪 测试文件语法: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析AST
        ast.parse(content)
        print(f"✅ 语法检查通过: {file_path}")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {file_path}")
        print(f"   错误信息: {e.msg}")
        print(f"   行号: {e.lineno}")
        print(f"   列号: {e.offset}")
        if e.text:
            print(f"   错误行: {e.text.strip()}")
        return False
        
    except FileNotFoundError:
        print(f"⚠️  文件不存在: {file_path}")
        return False
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_f_string_fix():
    """测试f-string修复"""
    print("🔧 测试f-string修复...")
    print("=" * 60)
    
    # 测试修复前的问题代码（模拟）
    print("❌ 修复前的问题代码:")
    problematic_code = '''
# 这种写法会导致SyntaxError
content = "Hello\\nWorld"
formatted = f"Content: {content.replace('\\n', '<br>')}"
'''
    print(problematic_code)
    
    try:
        ast.parse(problematic_code)
        print("意外：问题代码竟然通过了语法检查")
    except SyntaxError as e:
        print(f"预期的语法错误: {e.msg}")
    
    print()
    
    # 测试修复后的代码
    print("✅ 修复后的正确代码:")
    fixed_code = '''
# 正确的写法：先处理字符串，再放入f-string
content = "Hello\\nWorld"
formatted_content = content.replace('\\n', '<br>')
formatted = f"Content: {formatted_content}"
'''
    print(fixed_code)
    
    try:
        ast.parse(fixed_code)
        print("✅ 修复后的代码语法检查通过")
    except SyntaxError as e:
        print(f"❌ 修复后仍有语法错误: {e.msg}")

def test_case_content_formatting():
    """测试案件内容格式化"""
    print("📄 测试案件内容格式化...")
    print("=" * 60)
    
    # 模拟案件内容
    case_content = """2024年5月9日，罗添文驾驶桂E86181号宝骏汽车在沈海高速公路铁铺路段被查获，车内藏有南京炫赫门香烟2736条，价值492,480元。
经查，罗添文受不明上家雇佣，从事香烟运输活动。
案件正在进一步调查中。"""
    
    print("原始案件内容:")
    print(repr(case_content))
    print()
    
    # 测试格式化处理
    formatted_content = case_content.replace('\n', '<br>')
    print("格式化后的内容:")
    print(repr(formatted_content))
    print()
    
    # 测试在HTML中的显示效果
    html_template = f'''
    <div style="
        font-size: 16px;
        line-height: 1.8;
        color: #2c3e50;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #3498db;
        white-space: pre-wrap;
        font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        max-height: 400px;
        overflow-y: auto;
    ">
        {formatted_content}
    </div>
    '''
    
    print("HTML模板生成成功:")
    print(f"模板长度: {len(html_template)} 字符")
    print("✅ 案件内容格式化测试通过")

def analyze_fix():
    """分析修复内容"""
    print("📊 分析修复内容...")
    print("=" * 60)
    
    print("问题原因:")
    print("- f-string表达式中不能直接包含反斜杠字符")
    print("- Python语法限制：f'{expr}' 中的 expr 不能包含反斜杠")
    print("- 错误代码: f'{case_content.replace('\\n', '<br>')}'")
    print()
    
    print("修复方案:")
    print("- 将字符串处理操作移到f-string外部")
    print("- 先执行: formatted_content = case_content.replace('\\n', '<br>')")
    print("- 再使用: f'{formatted_content}'")
    print()
    
    print("修复效果:")
    print("✅ 消除语法错误")
    print("✅ 保持功能不变")
    print("✅ 提高代码可读性")
    print("✅ 符合Python最佳实践")

def test_streamlit_compatibility():
    """测试Streamlit兼容性"""
    print("🚀 测试Streamlit兼容性...")
    print("=" * 60)
    
    # 模拟Streamlit markdown组件的使用
    def mock_st_markdown(content, unsafe_allow_html=False):
        """模拟streamlit的markdown函数"""
        return f"Markdown rendered: {len(content)} characters, HTML: {unsafe_allow_html}"
    
    # 测试修复后的代码逻辑
    case_content = "测试内容\n第二行\n第三行"
    
    if case_content:
        # 先处理换行符替换，避免f-string中的反斜杠问题
        formatted_content = case_content.replace('\n', '<br>')
        html_content = f'''
        <div style="
            font-size: 16px;
            line-height: 1.8;
            color: #2c3e50;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            white-space: pre-wrap;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            max-height: 400px;
            overflow-y: auto;
        ">
            {formatted_content}
        </div>
        '''
        
        result = mock_st_markdown(html_content, unsafe_allow_html=True)
        print(f"✅ Streamlit markdown调用成功: {result}")
    else:
        print("ℹ️  无案件内容时的处理")
    
    print("✅ Streamlit兼容性测试通过")

if __name__ == "__main__":
    print("🔍 Python语法修复验证")
    print("=" * 80)
    print()
    
    # 测试主要文件的语法
    files_to_test = [
        "streamlit_app.py",
        "streamlit_app - 副本.py",
        "multi_agents.py"
    ]
    
    all_passed = True
    for file_path in files_to_test:
        if not test_python_syntax(file_path):
            all_passed = False
        print()
    
    # 其他测试
    test_f_string_fix()
    print()
    
    test_case_content_formatting()
    print()
    
    analyze_fix()
    print()
    
    test_streamlit_compatibility()
    print()
    
    # 总结
    if all_passed:
        print("🎉 所有语法检查通过！")
    else:
        print("❌ 仍有语法错误需要修复")
    
    print("🎯 修复完成，可以正常运行Streamlit应用了！")
