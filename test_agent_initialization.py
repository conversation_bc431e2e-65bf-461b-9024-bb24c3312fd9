#!/usr/bin/env python3
"""
测试智能体初始化和变量作用域问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_message_generation():
    """测试系统消息生成"""
    print("🧪 测试系统消息生成...")
    print()
    
    # 模拟 BatchCaseExtractionAgent 的 _get_system_message 方法
    def mock_get_system_message(user_requirements=None):
        try:
            # 处理用户需求字段
            if user_requirements:
                # 处理中文逗号替换为英文逗号
                user_requirements_new = user_requirements.replace("，", ",")
            else:
                # 默认字段
                user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
            
            # 统计字段数量
            user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])
            
            # 动态生成分析需求
            analysis_requirements = f"""任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{user_requirements_count}列）：
{user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。
最好按层级关系至上而下。

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 换行使用以下换行格式示例，不要使用\\n格式

2. 换行格式示例：
   graph TD
       A[张某某
       组织者
       35岁
       已判刑] -->|指挥| B[李某某
       运输员
       28岁
       在逃]
"""
            
            return f"""你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

{analysis_requirements}

返回格式：
{{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}}
"""
        except Exception as e:
            return f"Error generating system message: {e}"
    
    # 测试不同的输入
    test_cases = [
        None,  # 默认字段
        "",    # 空字符串
        "姓名,年龄,性别",  # 简单字段
        "实体类型，姓名，年龄，性别，职务",  # 中文逗号
        "姓名, 年龄, 性别, 职务, 组织, 关系",  # 带空格
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"📋 测试用例 {i}: {repr(test_input)}")
        print("=" * 60)
        
        try:
            result = mock_get_system_message(test_input)
            if result.startswith("Error"):
                print(f"❌ {result}")
            else:
                print("✅ 成功生成系统消息")
                print(f"消息长度: {len(result)} 字符")
                
                # 检查关键变量是否正确替换
                if "{user_requirements_new}" in result:
                    print("❌ 发现未替换的变量: user_requirements_new")
                elif "{user_requirements_count}" in result:
                    print("❌ 发现未替换的变量: user_requirements_count")
                else:
                    print("✅ 所有变量都已正确替换")
                
                # 显示前200字符
                print(f"前200字符: {result[:200]}...")
        except Exception as e:
            print(f"❌ 异常: {e}")
            print(f"异常类型: {type(e).__name__}")
        
        print()

def test_error_scenarios():
    """测试错误场景"""
    print("🔍 测试错误场景...")
    print()
    
    # 测试可能导致NameError的场景
    def problematic_f_string():
        try:
            # 这会导致NameError
            message = f"未定义变量: {undefined_variable}"
            return message
        except NameError as e:
            return f"NameError caught: {e}"
    
    def safe_f_string():
        # 安全的变量定义
        defined_variable = "已定义"
        message = f"已定义变量: {defined_variable}"
        return message
    
    print("❌ 问题场景:")
    print(problematic_f_string())
    print()
    
    print("✅ 安全场景:")
    print(safe_f_string())
    print()

def test_variable_scope():
    """测试变量作用域"""
    print("🔧 测试变量作用域...")
    print()
    
    def outer_function():
        # 外层函数定义变量
        outer_var = "外层变量"
        
        def inner_function():
            # 内层函数使用外层变量
            try:
                message = f"使用外层变量: {outer_var}"
                return message
            except NameError as e:
                return f"NameError: {e}"
        
        return inner_function()
    
    def problematic_scope():
        def inner_function():
            # 内层函数尝试使用未定义的变量
            try:
                message = f"使用未定义变量: {not_defined_var}"
                return message
            except NameError as e:
                return f"NameError: {e}"
        
        return inner_function()
    
    print("✅ 正确的作用域:")
    print(outer_function())
    print()
    
    print("❌ 错误的作用域:")
    print(problematic_scope())
    print()

def analyze_multi_agents_fix():
    """分析multi_agents.py的修复"""
    print("🛠️ 分析multi_agents.py的修复...")
    print()
    
    print("修复内容:")
    print("1. 在 _initialize_agent 方法中添加了异常处理")
    print("2. 在 extract_multiple_cases 方法中添加了初始化错误处理")
    print("3. 提供了默认值回退机制")
    print()
    
    print("修复原理:")
    print("- 捕获可能的 NameError 或其他异常")
    print("- 在出错时使用默认配置重新初始化")
    print("- 确保 agent 始终能够正确初始化")
    print()
    
    print("预期效果:")
    print("- 消除 'name user_requirements_new is not defined' 错误")
    print("- 提供更好的错误恢复机制")
    print("- 确保系统的稳定性")
    print()

if __name__ == "__main__":
    test_system_message_generation()
    test_error_scenarios()
    test_variable_scope()
    analyze_multi_agents_fix()
    print("🎉 智能体初始化测试完成!")
