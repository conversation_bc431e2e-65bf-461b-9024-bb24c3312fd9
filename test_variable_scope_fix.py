#!/usr/bin/env python3
"""
测试变量作用域修复效果
"""

def test_parse_response_manually_fix():
    """测试 _parse_response_manually 方法的修复"""
    print("🧪 测试 _parse_response_manually 方法的修复...")
    print()
    
    # 模拟 BatchCaseExtractionAgent 类
    class MockBatchCaseExtractionAgent:
        def __init__(self):
            # 全局用户需求变量
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            """设置用户需求并处理相关变量"""
            self.user_requirements = user_requirements
            
            # 处理用户需求字段
            if user_requirements:
                # 处理中文逗号替换为英文逗号
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                # 默认字段
                self.user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
            
            # 统计字段数量
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def _parse_response_manually(self, response: str):
            """手动解析响应 - 修复后的版本"""
            try:
                return {
                    "analysis": "分析内容提取中...",
                    "csv_data": f"{self.user_requirements_new}",
                    "mermaid_code": "graph TD\n    A[案件分析] --> B[人物关系]"
                }
            except Exception as e:
                return {
                    "error": f"解析失败: {e}",
                    "analysis": "解析失败",
                    "csv_data": "",
                    "mermaid_code": ""
                }
        
        def _parse_response_manually_old(self, response: str):
            """手动解析响应 - 修复前的版本（模拟错误）"""
            try:
                # 这里故意使用未定义的变量来模拟原来的错误
                return {
                    "analysis": "分析内容提取中...",
                    "csv_data": f"{user_requirements_new}",  # 这里会报错
                    "mermaid_code": "graph TD\n    A[案件分析] --> B[人物关系]"
                }
            except Exception as e:
                return {
                    "error": f"解析失败: {e}",
                    "analysis": "解析失败",
                    "csv_data": "",
                    "mermaid_code": ""
                }
    
    # 测试修复效果
    agent = MockBatchCaseExtractionAgent()
    
    # 设置用户需求
    agent._set_user_requirements("姓名,年龄,性别,职务,组织")
    
    print("📋 测试场景: AI响应解析失败，需要手动解析")
    print("=" * 60)
    
    # 测试修复前的版本（会报错）
    print("❌ 修复前的版本:")
    result_old = agent._parse_response_manually_old("无效响应")
    if "error" in result_old:
        print(f"  错误: {result_old['error']}")
    else:
        print(f"  成功: {result_old}")
    print()
    
    # 测试修复后的版本（应该成功）
    print("✅ 修复后的版本:")
    result_new = agent._parse_response_manually("无效响应")
    if "error" in result_new:
        print(f"  错误: {result_new['error']}")
    else:
        print(f"  成功: {result_new}")
        print(f"  CSV数据: {result_new['csv_data'][:50]}...")
    print()

def test_concurrent_scenarios():
    """测试并发场景下的变量一致性"""
    print("🔧 测试并发场景下的变量一致性...")
    print()
    
    class MockAgent:
        def __init__(self):
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            self.user_requirements = user_requirements
            if user_requirements:
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                self.user_requirements_new = "默认字段1,默认字段2,默认字段3"
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def process_case_a(self):
            """模拟处理案件A"""
            return f"案件A - 字段: {self.user_requirements_new[:20]}..., 数量: {self.user_requirements_count}"
        
        def process_case_b(self):
            """模拟处理案件B"""
            return f"案件B - CSV格式({self.user_requirements_count}列): {self.user_requirements_new[:30]}..."
        
        def parse_response_manually(self):
            """模拟手动解析响应"""
            return f"手动解析 - 使用字段: {self.user_requirements_new[:25]}..."
    
    # 模拟并发处理多个案件
    agents = []
    for i in range(3):
        agent = MockAgent()
        agent._set_user_requirements(f"字段{i+1},年龄,性别,职务")
        agents.append(agent)
    
    print("模拟并发处理多个案件:")
    for i, agent in enumerate(agents):
        print(f"智能体 {i+1}:")
        print(f"  {agent.process_case_a()}")
        print(f"  {agent.process_case_b()}")
        print(f"  {agent.parse_response_manually()}")
        print()

def test_error_recovery():
    """测试错误恢复机制"""
    print("🚨 测试错误恢复机制...")
    print()
    
    class MockAgent:
        def __init__(self):
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            self.user_requirements = user_requirements
            if user_requirements:
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                self.user_requirements_new = "默认字段1,默认字段2,默认字段3"
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def safe_operation(self):
            """安全操作，使用实例变量"""
            try:
                return f"成功: 字段数量={self.user_requirements_count}, 字段={self.user_requirements_new[:30]}..."
            except Exception as e:
                return f"错误: {e}"
        
        def unsafe_operation(self):
            """不安全操作，使用未定义变量"""
            try:
                # 故意使用未定义变量
                return f"成功: 字段数量={undefined_count}, 字段={undefined_fields}"
            except Exception as e:
                return f"错误: {e}"
    
    agent = MockAgent()
    
    print("测试未初始化状态:")
    print(f"  安全操作: {agent.safe_operation()}")
    print(f"  不安全操作: {agent.unsafe_operation()}")
    print()
    
    print("测试初始化后状态:")
    agent._set_user_requirements("姓名,年龄,性别")
    print(f"  安全操作: {agent.safe_operation()}")
    print(f"  不安全操作: {agent.unsafe_operation()}")
    print()

def analyze_fix_impact():
    """分析修复的影响"""
    print("📊 分析修复的影响...")
    print()
    
    print("修复的问题:")
    print("1. ❌ 原问题: _parse_response_manually 方法中使用了 user_requirements_new 而没有 self. 前缀")
    print("2. ❌ 影响范围: 只有在AI响应解析失败时才会调用此方法，所以只有个别案件报错")
    print("3. ❌ 错误信息: name 'user_requirements_new' is not defined")
    print()
    
    print("修复的方案:")
    print("1. ✅ 修改: 将 user_requirements_new 改为 self.user_requirements_new")
    print("2. ✅ 效果: 使用类的实例变量，确保变量在正确的作用域内")
    print("3. ✅ 一致性: 与其他方法保持一致，都使用 self. 前缀")
    print()
    
    print("预期效果:")
    print("1. ✅ 消除错误: 不再出现 NameError")
    print("2. ✅ 提高稳定性: 所有案件都能正常处理")
    print("3. ✅ 保持一致性: 所有方法都使用相同的变量访问方式")
    print()

if __name__ == "__main__":
    test_parse_response_manually_fix()
    test_concurrent_scenarios()
    test_error_recovery()
    analyze_fix_impact()
    print("🎉 变量作用域修复测试完成!")
