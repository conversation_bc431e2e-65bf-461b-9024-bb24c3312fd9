graph TD
    A[潘是彪（阿水）
    主谋
    逮捕
    收益2000元] --> B[叶爱军
    组织者
    取保候审]
    B --> C[张范
    送货司机
    取保候审]
    B --> D[胡兴平
    仓库打包
    取保候审]
    B --> E[张大健
    运输司机
    取保候审]
    A --> F[哑巴
    外部联络]
    style A fill:#ffcccc,stroke:#333
    style B fill:#ccffcc,stroke:#333
    style C fill:#ccccff,stroke:#333
    style D fill:#ccccff,stroke:#333
    style E fill:#ccccff,stroke:#333
    style F fill:#ffffcc,stroke:#333
    classDef 主谋 fill:#ffcccc,stroke:#333;
    classDef 组织者 fill:#ccffcc,stroke:#333;
    classDef 执行层 fill:#ccccff,stroke:#333;
    classDef 外部 fill:#ffffcc,stroke:#333;
    class A 主谋
    class B 组织者
    class C,D,E 执行层
    class F 外部