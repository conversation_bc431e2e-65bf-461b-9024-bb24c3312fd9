# 全局变量一致性修改完成报告

## 🎯 问题描述

之前的代码中，`user_requirements`、`user_requirements_new`、`user_requirements_count` 这三个变量在不同方法中重复处理，导致：

1. **变量作用域问题**：`name 'user_requirements_new' is not defined` 错误
2. **数据不一致**：不同方法中处理结果可能不同
3. **代码重复**：相同的处理逻辑在多个地方重复
4. **维护困难**：修改逻辑需要在多个地方同步更新

## ✅ 解决方案

将这三个变量提升为 `BatchCaseExtractionAgent` 类的实例变量，确保全局一致性。

### 1. 类变量定义

在 `__init__` 方法中添加全局变量：

```python
def __init__(self, model_manager: Model<PERSON>anager, session_manager: SessionManager, max_concurrent: int = 10):
    self.model_client = model_manager.get_client()
    self.session_manager = session_manager
    self.max_concurrent = max_concurrent
    self.agent = None  # 延迟初始化，等待用户需求
    
    # 全局用户需求变量
    self.user_requirements = None
    self.user_requirements_new = None
    self.user_requirements_count = 0
```

### 2. 统一处理方法

添加 `_set_user_requirements` 方法统一处理：

```python
def _set_user_requirements(self, user_requirements: str = None):
    """设置用户需求并处理相关变量"""
    self.user_requirements = user_requirements
    
    # 处理用户需求字段
    if user_requirements:
        # 处理中文逗号替换为英文逗号
        self.user_requirements_new = user_requirements.replace("，", ",")
    else:
        # 默认字段
        self.user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
    
    # 统计字段数量
    self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
```

### 3. 方法修改

#### `_initialize_agent` 方法
```python
def _initialize_agent(self, user_requirements: str = None):
    """初始化或重新初始化智能体"""
    try:
        # 设置用户需求
        self._set_user_requirements(user_requirements)
        
        # 生成系统消息
        system_message = self._get_system_message()
        self.agent = AssistantAgent(
            name="batch_case_extractor",
            model_client=self.model_client,
            system_message=system_message
        )
    except Exception as e:
        print(f"Error initializing agent: {e}")
        # 使用默认系统消息作为后备
        self._set_user_requirements(None)
        default_system_message = self._get_system_message()
        self.agent = AssistantAgent(
            name="batch_case_extractor",
            model_client=self.model_client,
            system_message=default_system_message
        )
```

#### `_get_system_message` 方法
```python
def _get_system_message(self) -> str:
    """生成系统消息，使用类的全局变量"""
    # 动态生成分析需求
    analysis_requirements = f"""任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{self.user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{self.user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{self.user_requirements_count}列）：
{self.user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。
最好按层级关系至上而下。

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 换行使用以下换行格式示例，不要使用\\n格式

2. 换行格式示例：
   graph TD
       A[张某某
       组织者
       35岁
       已判刑] -->|指挥| B[李某某
       运输员
       28岁
       在逃]
"""
    
    return f"""你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

{analysis_requirements}

返回格式：
{{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}}
"""
```

#### `extract_multiple_cases` 方法
```python
# 初始化智能体（如果还没有初始化或需求发生变化）
if self.agent is None or self.user_requirements != user_requirements:
    try:
        self._initialize_agent(user_requirements)
    except Exception as e:
        print(f"Error initializing agent in extract_multiple_cases: {e}")
        # 使用默认需求重新尝试
        self._initialize_agent(None)
```

#### CSV Header 生成
```python
header = f"批次号,承办单位,案件编号,案件名称,{self.user_requirements_new}"
```

## 🎯 修改效果

### 1. 解决变量作用域问题
- ✅ **消除 NameError**：所有变量都在类的实例范围内定义
- ✅ **统一访问方式**：使用 `self.variable_name` 访问
- ✅ **避免重复定义**：变量只在一个地方定义和处理

### 2. 确保数据一致性
- ✅ **单一数据源**：所有方法使用相同的实例变量
- ✅ **同步更新**：修改需求时，所有相关变量同时更新
- ✅ **状态保持**：变量状态在整个对象生命周期内保持

### 3. 简化代码维护
- ✅ **减少重复代码**：处理逻辑集中在 `_set_user_requirements` 方法
- ✅ **统一修改点**：需要调整处理逻辑时只需修改一个地方
- ✅ **清晰的职责分离**：每个方法职责明确

### 4. 提高系统稳定性
- ✅ **错误处理**：添加异常处理和默认值回退
- ✅ **状态检查**：检查需求是否发生变化，决定是否重新初始化
- ✅ **一致性保证**：确保所有使用这些变量的地方都获得相同的值

## 📊 修改文件清单

### 已修改的文件
- ✅ `multi_agents.py`
  - 第411-420行：添加全局变量定义
  - 第422-436行：添加 `_set_user_requirements` 方法
  - 第437-459行：修改 `_initialize_agent` 方法
  - 第461-462行：修改 `_get_system_message` 方法签名
  - 第464-475行：使用全局变量的分析需求模板
  - 第480-481行：使用全局变量的CSV格式
  - 第525-532行：简化 `extract_multiple_cases` 初始化逻辑
  - 第598行：使用全局变量的CSV header生成

## 🚀 使用效果

### 开发者体验
- **更清晰的代码结构**：变量定义和使用逻辑更加清晰
- **更容易调试**：可以直接检查实例变量的状态
- **更好的可维护性**：修改处理逻辑只需要在一个地方进行

### 系统稳定性
- **消除运行时错误**：不再出现变量未定义的错误
- **数据一致性保证**：所有地方使用的变量值完全一致
- **更好的错误恢复**：出错时有默认值回退机制

### 功能完整性
- **动态字段支持**：支持用户自定义任意字段
- **中文逗号处理**：自动处理中文逗号转换
- **字段数量统计**：自动统计和验证字段数量
- **CSV格式生成**：动态生成对应的CSV格式

## 🎉 总结

通过将 `user_requirements`、`user_requirements_new`、`user_requirements_count` 提升为类的实例变量，实现了：

1. **全局一致性**：所有方法使用相同的变量值
2. **错误消除**：解决了变量作用域导致的 NameError
3. **代码简化**：减少了重复的处理逻辑
4. **维护性提升**：统一的修改点和清晰的职责分离
5. **稳定性增强**：更好的错误处理和状态管理

现在系统中的这三个关键变量在全局范围内保持完全一致，确保了数据的准确性和系统的稳定性！
