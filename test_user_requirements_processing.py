#!/usr/bin/env python3
"""
测试用户需求字段处理功能
"""

def test_user_requirements_processing():
    """测试用户需求字段处理"""
    
    print("🧪 测试用户需求字段处理功能...")
    print()
    
    # 测试用例1：默认字段
    default_user_requirements = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
    
    # 测试用例2：包含中文逗号的字段
    chinese_comma_requirements = "实体类型（人员/公司/组织），姓名/代号/公司/昵称，性别，年龄，身份证号，户籍地/现居地，文化程度，直接上级，所属公司，所属组织，所属组织层级，角色分工，直接关联人物，直接关联关系，关联工具，关联物品，关联行为，关联场所，司法处置结果，经济收益（元）"
    
    # 测试用例3：自定义字段
    custom_requirements = "姓名,年龄,性别,职务,组织,关系,状态"
    
    test_cases = [
        ("默认字段", default_user_requirements),
        ("中文逗号字段", chinese_comma_requirements),
        ("自定义字段", custom_requirements)
    ]
    
    for name, user_requirements in test_cases:
        print(f"📋 测试案例: {name}")
        print("=" * 60)
        
        # 处理用户需求：替换中文逗号为英文逗号
        user_requirements_new = user_requirements.replace("，", ",")
        
        # 统计标签数量
        user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])
        
        print(f"原始字段: {user_requirements}")
        print(f"处理后字段: {user_requirements_new}")
        print(f"字段数量: {user_requirements_count}")
        
        # 生成分析需求模板
        analysis_requirements = f"""
任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{user_requirements_count}列）：
{user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。
最好按层级关系至上而下。 

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 换行使用以下换行格式示例，不要使用\\n格式

2. 换行格式示例：
   graph TD
       A[张某某
       组织者
       35岁
       已判刑] -->|指挥| B[李某某
       运输员
       28岁
       在逃]
"""
        
        print(f"生成的分析需求模板:")
        print(analysis_requirements)
        print()
        
        # 生成CSV header
        header = f"批次号,承办单位,案件编号,案件名称,{user_requirements_new}"
        print(f"生成的CSV header:")
        print(header)
        print()
        print("-" * 80)
        print()

def test_field_validation():
    """测试字段验证"""
    print("🔍 测试字段验证功能...")
    print()
    
    # 测试不同的字段格式
    test_fields = [
        "姓名,年龄,性别",  # 简单字段
        "姓名，年龄，性别",  # 中文逗号
        "姓名, 年龄, 性别",  # 带空格
        "姓名，年龄, 性别，职务",  # 混合格式
        "",  # 空字段
        "   ",  # 空白字符
        "姓名",  # 单个字段
    ]
    
    for fields in test_fields:
        print(f"测试字段: '{fields}'")
        
        # 处理字段
        processed_fields = fields.replace("，", ",")
        field_list = [field.strip() for field in processed_fields.split(",") if field.strip()]
        field_count = len(field_list)
        
        print(f"  处理后: '{processed_fields}'")
        print(f"  字段列表: {field_list}")
        print(f"  字段数量: {field_count}")
        
        if field_count == 0:
            print("  ⚠️  警告: 没有有效字段")
        elif field_count < 5:
            print("  ⚠️  警告: 字段数量较少")
        else:
            print("  ✅ 字段数量正常")
        
        print()

def test_system_message_generation():
    """测试系统消息生成"""
    print("🤖 测试系统消息生成...")
    print()
    
    # 模拟 _get_system_message 方法的逻辑
    def mock_get_system_message(user_requirements=None):
        # 处理用户需求字段
        if user_requirements:
            # 处理中文逗号替换为英文逗号
            user_requirements_new = user_requirements.replace("，", ",")
        else:
            # 默认字段
            user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
        
        # 统计字段数量
        user_requirements_count = len([field.strip() for field in user_requirements_new.split(",") if field.strip()])
        
        return f"""你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{user_requirements_count}列）：
{user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。
最好按层级关系至上而下。 

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 换行使用以下换行格式示例，不要使用\\n格式

2. 换行格式示例：
   graph TD
       A[张某某
       组织者
       35岁
       已判刑] -->|指挥| B[李某某
       运输员
       28岁
       在逃]

返回格式：
{{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}}
"""
    
    # 测试不同的用户需求
    test_requirements = [
        None,  # 使用默认字段
        "姓名,年龄,性别,职务,组织",  # 简单字段
        "实体类型，姓名，性别，年龄，职务，组织，关系，状态"  # 中文逗号字段
    ]
    
    for i, req in enumerate(test_requirements, 1):
        print(f"测试用例 {i}: {req if req else '默认字段'}")
        print("=" * 60)
        
        system_message = mock_get_system_message(req)
        print(system_message[:500] + "..." if len(system_message) > 500 else system_message)
        print()

if __name__ == "__main__":
    test_user_requirements_processing()
    test_field_validation()
    test_system_message_generation()
    print("🎉 用户需求字段处理测试完成!")
