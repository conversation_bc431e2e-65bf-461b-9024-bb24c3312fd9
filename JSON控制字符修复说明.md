# JSON控制字符修复说明

## 🚨 问题描述

在案件信息提取过程中，出现了JSON解析错误：

```
ERROR - 单个案件信息提取失败: Invalid control character at: line 3 column 168 (char 387)
```

## 🔍 问题原因

AI模型返回的JSON响应中包含了不合法的控制字符，这些字符在JSON标准中是不允许的：

- **控制字符范围**: ASCII 0x00-0x1F (除了 `\t` `\n` `\r`)
- **常见问题字符**: `\x0C` (换页符), `\x08` (退格符), `\x0B` (垂直制表符) 等
- **JSON标准**: 只允许转义的控制字符如 `\n`, `\r`, `\t`, `\"`, `\\`, `\/`

## ✅ 修复方案

### 1. 新增JSON清理函数

在所有涉及JSON解析的类中添加 `clean_json_string` 方法：

```python
def clean_json_string(self, json_string: str) -> str:
    """
    清理JSON字符串中的控制字符，确保能正确解析
    
    Args:
        json_string (str): 原始的JSON字符串
        
    Returns:
        str: 清理后的JSON字符串
    """
    if not json_string:
        return json_string
        
    # 移除或替换常见的控制字符
    # 保留合法的JSON转义字符：\n, \r, \t, \", \\, \/
    import re
    
    # 替换不合法的控制字符
    # ASCII控制字符范围：0x00-0x1F (除了 \t \n \r)
    json_string = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F]', '', json_string)
    
    return json_string
```

### 2. 更新JSON解析逻辑

将原来的直接解析：

```python
# 旧代码
try:
    result = json.loads(model_response)
except json.JSONDecodeError:
    # 错误处理...
```

更新为先清理再解析：

```python
# 新代码
try:
    # 先清理JSON字符串中的控制字符
    cleaned_response = self.clean_json_string(model_response)
    result = json.loads(cleaned_response)
except json.JSONDecodeError:
    # 尝试提取JSON
    json_match = re.search(r'\{.*\}', model_response, re.DOTALL)
    if json_match:
        try:
            # 清理提取的JSON字符串
            cleaned_json = self.clean_json_string(json_match.group(0))
            result = json.loads(cleaned_json)
        except json.JSONDecodeError:
            # 如果清理后仍然解析失败，使用手动解析
            result = self._parse_response_manually(model_response)
    else:
        # 手动解析
        result = self._parse_response_manually(model_response)
```

## 📁 修复文件列表

### 主要文件
1. **agents.py** - CaseExtractionAgent类
   - 添加 `clean_json_string` 方法
   - 更新JSON解析逻辑 (第554-574行)

2. **multi_agents.py** - MultiAgentSystem类
   - 添加 `_clean_json_string` 方法
   - 更新JSON解析逻辑 (第626-646行)

3. **-multi_agents.py** - RepairAgent和BatchCaseExtractionAgent类
   - RepairAgent: 添加 `_clean_json_string` 方法，更新两处JSON解析
   - BatchCaseExtractionAgent: 添加 `_clean_json_string` 方法，更新JSON解析

## 🧪 测试验证

创建了 `test_json_fix.py` 测试脚本，验证修复效果：

### 测试结果
- ✅ **控制字符清理**: 成功移除 `\x0C`, `\x08` 等控制字符
- ✅ **JSON解析修复**: 原本解析失败的JSON现在可以正常解析
- ✅ **正常JSON不受影响**: 清理函数不会破坏正常的JSON结构
- ✅ **实际场景模拟**: 成功修复类似 "line 3 column 168" 的错误

### 测试输出示例
```
✅ 直接解析失败 (预期): Invalid control character at: line 2 column 24 (char 25)
✅ 清理后解析成功!
✅ 正常JSON解析成功
✅ 清理函数不影响正常JSON
✅ 模拟错误JSON解析失败 (预期): Invalid control character at: line 3 column 123 (char 125)
✅ 清理后解析成功!
```

## 🎯 修复效果

### 解决的问题
1. **消除JSON解析错误**: 不再出现 "Invalid control character" 错误
2. **提高系统稳定性**: 避免因JSON解析失败导致的案件处理中断
3. **保持数据完整性**: 清理控制字符的同时保留有效内容
4. **向后兼容**: 对正常JSON没有任何负面影响

### 技术优势
- **轻量级解决方案**: 只需要一个简单的正则表达式替换
- **高效处理**: 清理操作性能开销极小
- **全面覆盖**: 涵盖所有JSON解析场景
- **易于维护**: 代码简洁，逻辑清晰

## 🔧 使用说明

修复后，系统会自动处理包含控制字符的JSON响应，用户无需进行任何额外操作。如果遇到JSON解析问题，系统会：

1. 首先尝试清理控制字符后解析
2. 如果仍然失败，尝试提取JSON片段并清理
3. 最后使用手动解析作为备用方案

这确保了系统的健壮性和可靠性。
