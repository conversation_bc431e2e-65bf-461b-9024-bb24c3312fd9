#!/usr/bin/env python3
"""
测试JSON控制字符修复功能
"""

import json
import re

def clean_json_string(json_string: str) -> str:
    """
    清理JSON字符串中的控制字符，确保能正确解析
    
    Args:
        json_string (str): 原始的JSON字符串
        
    Returns:
        str: 清理后的JSON字符串
    """
    if not json_string:
        return json_string
        
    # 移除或替换常见的控制字符
    # 保留合法的JSON转义字符：\n, \r, \t, \", \\, \/
    
    # 替换不合法的控制字符
    # ASCII控制字符范围：0x00-0x1F (除了 \t \n \r)
    json_string = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F]', '', json_string)
    
    return json_string

def test_json_cleaning():
    """测试JSON清理功能"""
    print("🧪 测试JSON控制字符清理功能...")
    
    # 测试用例1：包含控制字符的JSON
    test_json_1 = '{\n    "analysis": "这是一个测试\x0C分析",\n    "csv_data": "数据1,数据2\x08",\n    "mermaid_code": "graph TD\\n    A --> B"\n}'
    
    print(f"原始JSON (包含控制字符): {repr(test_json_1)}")
    
    try:
        # 尝试直接解析
        result = json.loads(test_json_1)
        print("❌ 直接解析成功 - 这不应该发生")
    except json.JSONDecodeError as e:
        print(f"✅ 直接解析失败 (预期): {e}")
        
        # 使用清理函数
        cleaned_json = clean_json_string(test_json_1)
        print(f"清理后JSON: {repr(cleaned_json)}")
        
        try:
            result = json.loads(cleaned_json)
            print("✅ 清理后解析成功!")
            print(f"解析结果: {result}")
        except json.JSONDecodeError as e:
            print(f"❌ 清理后仍然解析失败: {e}")
    
    # 测试用例2：正常的JSON
    test_json_2 = '{"analysis": "正常分析", "csv_data": "正常数据", "mermaid_code": "graph TD\\n    A --> B"}'
    
    print(f"\n正常JSON: {repr(test_json_2)}")
    
    try:
        result = json.loads(test_json_2)
        print("✅ 正常JSON解析成功")
        
        # 测试清理函数对正常JSON的影响
        cleaned_json = clean_json_string(test_json_2)
        result_cleaned = json.loads(cleaned_json)
        
        if result == result_cleaned:
            print("✅ 清理函数不影响正常JSON")
        else:
            print("❌ 清理函数影响了正常JSON")
            
    except json.JSONDecodeError as e:
        print(f"❌ 正常JSON解析失败: {e}")
    
    # 测试用例3：模拟实际错误情况
    # "Invalid control character at: line 3 column 168 (char 387)"
    test_json_3 = '''
{
    "analysis": "经查，犯罪嫌疑人张某某组织走私香烟团伙，通过海上偷运方式走私香烟入境。该团伙分工明确，张某某负责组织协调，李某某负责运输，王某某负责销售。查明该团伙走私香烟价值500万元，现场查获走私香烟1000箱，账本若干。\x0C这里有一个控制字符",
    "csv_data": "实体类型,姓名/代号,性别,年龄\\n个人,张某某,男,35\\n个人,李某某,男,28\\n个人,王某某,女,32",
    "mermaid_code": "graph TD\\n    A[张某某] --> B[李某某]\\n    A --> C[王某某]"
}
'''
    
    print(f"\n模拟实际错误JSON: {repr(test_json_3[:200])}...")
    
    try:
        result = json.loads(test_json_3)
        print("❌ 模拟错误JSON直接解析成功 - 这不应该发生")
    except json.JSONDecodeError as e:
        print(f"✅ 模拟错误JSON解析失败 (预期): {e}")
        
        # 使用清理函数
        cleaned_json = clean_json_string(test_json_3)
        
        try:
            result = json.loads(cleaned_json)
            print("✅ 清理后解析成功!")
            print(f"分析内容长度: {len(result['analysis'])}")
            print(f"CSV数据: {result['csv_data'][:50]}...")
            print(f"Mermaid代码: {result['mermaid_code'][:50]}...")
        except json.JSONDecodeError as e:
            print(f"❌ 清理后仍然解析失败: {e}")

if __name__ == "__main__":
    test_json_cleaning()
    print("\n🎯 JSON控制字符清理功能测试完成!")
