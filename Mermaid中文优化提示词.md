# Mermaid中文文本优化提示词

## 🎯 问题描述

当前Mermaid图表中的中文文本换行效果不佳，需要优化提示词来生成更适合中文显示的Mermaid代码。

## 📋 优化后的Mermaid生成指导

### 1. 中文文本格式化规则

```
Mermaid关系图生成要求：

1. **节点文本格式化**：
   - 每个节点的中文文本应控制在合适长度
   - 姓名单独一行，不超过6个中文字符
   - 角色/职务单独一行，不超过8个中文字符  
   - 年龄/状态信息单独一行，不超过10个中文字符
   - 金额/数量信息单独一行，不超过12个中文字符

2. **换行策略**：
   - 使用 \n 进行换行
   - 避免单行文本过长（建议不超过8个中文字符）
   - 重要信息优先显示在前面

3. **节点命名规范**：
   - 节点ID使用简短英文字母（A, B, C...）
   - 节点显示文本使用中文，用方括号包围
   - 示例：A[张某某\n主犯\n35岁\n已判刑]

4. **关系标签**：
   - 关系标签使用简短中文，不超过4个字符
   - 示例：|雇佣|、|指挥|、|运输|、|销售|

5. **样式设置**：
   - 为不同类型的节点设置不同颜色
   - 主犯：#FFB6C1 (浅粉色)
   - 从犯：#87CEEB (天蓝色)  
   - 物品：#98FB98 (浅绿色)
   - 场所：#FFD700 (金色)
   - 工具：#DDA0DD (紫色)
```

### 2. 优化示例

**优化前（文本过长）**：
```mermaid
A[刘定富非法经营者52岁取保候审获利25000元]
```

**优化后（合理换行）**：
```mermaid
A[刘定富\n非法经营\n52岁\n取保候审\n获利2.5万元]
```

### 3. 完整的Mermaid生成模板

```
请按照以下格式生成Mermaid关系图：

graph TD
    A[主要人物1\n角色描述\n年龄/状态] -->|关系| B[相关人物2\n角色描述\n年龄/状态]
    A -->|行为| C[涉案物品\n数量/价值]
    B -->|使用| D[工具/车辆\n型号/特征]
    C -->|查获于| E[地点/时间]
    
    style A fill:#FFB6C1,stroke:#333
    style B fill:#87CEEB,stroke:#333
    style C fill:#98FB98,stroke:#333
    style D fill:#DDA0DD,stroke:#333
    style E fill:#FFD700,stroke:#333
    
    classDef 主犯 fill:#FFB6C1,stroke:#333;
    classDef 从犯 fill:#87CEEB,stroke:#333;
    classDef 物品 fill:#98FB98,stroke:#333;
    classDef 工具 fill:#DDA0DD,stroke:#333;
    classDef 场所 fill:#FFD700,stroke:#333;

注意事项：
1. 每行中文文本不超过8个字符
2. 数字金额使用万元、千元等简化表示
3. 时间使用简短格式（如：2024/5/9）
4. 避免使用特殊符号，用中文描述
5. 节点间关系要清晰明确
```

## 🔧 实施方案

### 方案1：修改系统提示词（推荐）

在现有的系统提示词中添加Mermaid格式化指导：

```python
# 在系统消息中添加
mermaid_formatting_guide = """
第三步：Mermaid关系图生成
生成符合以下规范的Mermaid关系图：

1. 节点文本格式化：
   - 姓名：不超过6个中文字符一行
   - 角色：不超过8个中文字符一行  
   - 状态：不超过10个中文字符一行
   - 使用\\n换行，避免单行过长

2. 示例格式：
   A[张某某\\n组织者\\n35岁\\n已判刑] -->|指挥| B[李某某\\n运输员\\n28岁\\n在逃]

3. 样式设置：
   - 主犯：#FFB6C1
   - 从犯：#87CEEB  
   - 物品：#98FB98
   - 场所：#FFD700
   - 工具：#DDA0DD
"""
```

### 方案2：后处理优化

在生成Mermaid代码后，进行文本长度检查和自动换行：

```python
def optimize_mermaid_chinese_text(mermaid_code: str) -> str:
    """优化Mermaid代码中的中文文本显示"""
    import re
    
    def format_node_text(match):
        node_id = match.group(1)
        content = match.group(2)
        
        # 按\\n分割现有内容
        lines = content.split('\\n')
        formatted_lines = []
        
        for line in lines:
            # 如果单行中文字符超过8个，尝试分割
            if len(line) > 8 and any('\u4e00' <= char <= '\u9fff' for char in line):
                # 简单分割策略：在合适位置断行
                if len(line) > 12:
                    mid = len(line) // 2
                    formatted_lines.append(line[:mid])
                    formatted_lines.append(line[mid:])
                else:
                    formatted_lines.append(line)
            else:
                formatted_lines.append(line)
        
        return f"{node_id}[{'\\n'.join(formatted_lines)}]"
    
    # 匹配节点定义模式
    pattern = r'(\w+)\[([^\]]+)\]'
    optimized_code = re.sub(pattern, format_node_text, mermaid_code)
    
    return optimized_code
```

## 🎯 推荐实施

我建议采用**方案1**，直接在系统提示词中加入详细的中文格式化指导，这样AI在生成时就会考虑中文显示效果。
