#!/usr/bin/env python3
"""
测试全局变量一致性
"""

def test_global_variables_consistency():
    """测试全局变量一致性"""
    print("🧪 测试全局变量一致性...")
    print()
    
    # 模拟 BatchCaseExtractionAgent 类
    class MockBatchCaseExtractionAgent:
        def __init__(self):
            # 全局用户需求变量
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            """设置用户需求并处理相关变量"""
            self.user_requirements = user_requirements
            
            # 处理用户需求字段
            if user_requirements:
                # 处理中文逗号替换为英文逗号
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                # 默认字段
                self.user_requirements_new = "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）"
            
            # 统计字段数量
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def _get_system_message(self) -> str:
            """生成系统消息，使用类的全局变量"""
            analysis_requirements = f"""任务目标：
1. 组织架构解析（主犯/从犯认定、嫌疑人之间关系确认、分工逻辑）
2. 结构化数据提取（CSV格式，{self.user_requirements_count}列严格校验）
3. 多层级关系图谱（Mermaid语法+可视化规范）

执行步骤：
第一步：组织关系深度分析
深入分析理解并推理案件中的组织人物关系和相关信息，梳理谁是主犯，谁是从犯，谁负责组织，谁负责执行，这些组织和人是怎么分工做案。

第二步：要素提取
提取案件中的所有提及到的人物/公司/组织/代号/昵称信息：{self.user_requirements_new}
"年龄"要素提取要求：
其中"年龄" 要素,优先从案件内容中分析获取,若无法直接获得"年龄"要素,则通过"录入时间" 和 身份证号 中的年龄计算得到"年龄"

"姓名/公司/代号/昵称"要素提取要求：如果是代号或昵称尽量注明是哪来的昵称。

输出CSV格式（{self.user_requirements_count}列）：
{self.user_requirements_new}

第三步：关系图谱
梳理犯罪嫌疑人之间以及组织或公司之间的多层级关系或复杂关系，犯罪嫌疑人的信息需要丰富一些（例如角色，处置结果等关键信息），每条线上标上关系，生成Mermaid格式的关系图代码。
关系图要清晰易读，关系网络要全，但不累赘, 注意Mermaid代码换行语法要正确。
最好按层级关系至上而下。

Mermaid中文文本格式要求：
1. 节点文本格式化：
   - 每行文本不超过6-8个中文字符
   - 姓名单独一行
   - 年龄、学历、状态等信息分行显示
   - 金额信息使用万元、千元等简化表示
   - 换行使用以下换行格式示例，不要使用\\n格式

2. 换行格式示例：
   graph TD
       A[张某某
       组织者
       35岁
       已判刑] -->|指挥| B[李某某
       运输员
       28岁
       在逃]
"""
            
            return f"""你是一位走私案件资深分析专家，需要完成案件分析和要素提取。

{analysis_requirements}

返回格式：
{{
    "analysis": "组织关系分析",
    "csv_data": "CSV格式的提取数据",
    "mermaid_code": "Mermaid关系图代码"
}}
"""
        
        def get_csv_header(self):
            """生成CSV header"""
            return f"批次号,承办单位,案件编号,案件名称,{self.user_requirements_new}"
        
        def get_variables_info(self):
            """获取变量信息"""
            return {
                "user_requirements": self.user_requirements,
                "user_requirements_new": self.user_requirements_new,
                "user_requirements_count": self.user_requirements_count
            }
    
    # 测试不同的输入
    test_cases = [
        ("默认字段", None),
        ("简单字段", "姓名,年龄,性别,职务,组织"),
        ("中文逗号字段", "实体类型，姓名，年龄，性别，职务，组织，关系"),
        ("复杂字段", "实体类型（人员/公司/组织）,姓名/代号/公司/昵称,性别,年龄,身份证号,户籍地/现居地,文化程度,直接上级,所属公司,所属组织,所属组织层级,角色分工,直接关联人物,直接关联关系,关联工具，关联物品,关联行为,关联场所,司法处置结果,经济收益（元）")
    ]
    
    for name, user_requirements in test_cases:
        print(f"📋 测试用例: {name}")
        print("=" * 60)
        
        # 创建智能体实例
        agent = MockBatchCaseExtractionAgent()
        
        # 设置用户需求
        agent._set_user_requirements(user_requirements)
        
        # 获取变量信息
        vars_info = agent.get_variables_info()
        
        print(f"原始需求: {repr(vars_info['user_requirements'])}")
        print(f"处理后需求: {vars_info['user_requirements_new'][:50]}{'...' if len(vars_info['user_requirements_new']) > 50 else ''}")
        print(f"字段数量: {vars_info['user_requirements_count']}")
        
        # 生成系统消息
        try:
            system_message = agent._get_system_message()
            print("✅ 系统消息生成成功")
            
            # 检查变量是否正确替换
            if "{self.user_requirements_new}" in system_message:
                print("❌ 发现未替换的变量: self.user_requirements_new")
            elif "{self.user_requirements_count}" in system_message:
                print("❌ 发现未替换的变量: self.user_requirements_count")
            else:
                print("✅ 所有变量都已正确替换")
        except Exception as e:
            print(f"❌ 系统消息生成失败: {e}")
        
        # 生成CSV header
        try:
            csv_header = agent.get_csv_header()
            print(f"CSV Header: {csv_header[:80]}{'...' if len(csv_header) > 80 else ''}")
            print("✅ CSV Header生成成功")
        except Exception as e:
            print(f"❌ CSV Header生成失败: {e}")
        
        print()

def test_variable_consistency_across_methods():
    """测试方法间变量一致性"""
    print("🔧 测试方法间变量一致性...")
    print()
    
    class MockAgent:
        def __init__(self):
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            self.user_requirements = user_requirements
            if user_requirements:
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                self.user_requirements_new = "默认字段1,默认字段2,默认字段3"
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def method_a(self):
            """方法A使用全局变量"""
            return f"方法A - 字段数量: {self.user_requirements_count}, 字段: {self.user_requirements_new[:30]}..."
        
        def method_b(self):
            """方法B使用全局变量"""
            return f"方法B - CSV格式({self.user_requirements_count}列): {self.user_requirements_new}"
        
        def method_c(self):
            """方法C使用全局变量"""
            header = f"批次号,承办单位,案件编号,案件名称,{self.user_requirements_new}"
            return f"方法C - Header: {header[:50]}..."
    
    # 测试一致性
    agent = MockAgent()
    
    # 设置需求
    test_requirements = "姓名，年龄，性别，职务"
    agent._set_user_requirements(test_requirements)
    
    print(f"设置的需求: {test_requirements}")
    print(f"全局变量状态:")
    print(f"  user_requirements: {agent.user_requirements}")
    print(f"  user_requirements_new: {agent.user_requirements_new}")
    print(f"  user_requirements_count: {agent.user_requirements_count}")
    print()
    
    print("各方法使用全局变量的结果:")
    print(f"  {agent.method_a()}")
    print(f"  {agent.method_b()}")
    print(f"  {agent.method_c()}")
    print()
    
    # 修改需求，测试一致性
    new_requirements = "实体类型,姓名,年龄,性别,职务,组织,关系,状态"
    agent._set_user_requirements(new_requirements)
    
    print(f"修改后的需求: {new_requirements}")
    print(f"更新后的全局变量状态:")
    print(f"  user_requirements: {agent.user_requirements}")
    print(f"  user_requirements_new: {agent.user_requirements_new}")
    print(f"  user_requirements_count: {agent.user_requirements_count}")
    print()
    
    print("各方法使用更新后全局变量的结果:")
    print(f"  {agent.method_a()}")
    print(f"  {agent.method_b()}")
    print(f"  {agent.method_c()}")
    print()

def test_error_scenarios():
    """测试错误场景"""
    print("🚨 测试错误场景...")
    print()
    
    class MockAgent:
        def __init__(self):
            self.user_requirements = None
            self.user_requirements_new = None
            self.user_requirements_count = 0
        
        def _set_user_requirements(self, user_requirements: str = None):
            self.user_requirements = user_requirements
            if user_requirements:
                self.user_requirements_new = user_requirements.replace("，", ",")
            else:
                self.user_requirements_new = "默认字段1,默认字段2,默认字段3"
            self.user_requirements_count = len([field.strip() for field in self.user_requirements_new.split(",") if field.strip()])
        
        def test_uninitialized_variables(self):
            """测试未初始化变量的情况"""
            try:
                message = f"字段数量: {self.user_requirements_count}, 字段: {self.user_requirements_new}"
                return f"✅ 成功: {message[:50]}..."
            except Exception as e:
                return f"❌ 错误: {e}"
    
    agent = MockAgent()
    
    print("测试未初始化变量:")
    result = agent.test_uninitialized_variables()
    print(f"  {result}")
    print()
    
    print("初始化后测试:")
    agent._set_user_requirements("姓名,年龄,性别")
    result = agent.test_uninitialized_variables()
    print(f"  {result}")
    print()

if __name__ == "__main__":
    test_global_variables_consistency()
    test_variable_consistency_across_methods()
    test_error_scenarios()
    print("🎉 全局变量一致性测试完成!")
