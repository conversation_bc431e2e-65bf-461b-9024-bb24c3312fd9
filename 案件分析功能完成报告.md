# 案件分析功能完成报告

## 🎯 功能需求

根据您的要求，新增了"案件分析"用户输入框，允许用户自定义案件分析专家角色，并实现了动态的系统消息生成逻辑。

## ✅ 已完成的修改

### 1. 后端修改 - multi_agents.py

#### 新增全局变量
```python
# 全局用户需求变量
self.user_requirements = None
self.user_requirements_new = None
self.user_requirements_count = 0
self.user_relationship_images = None
self.user_analysis = None  # 新增
```

#### 修改 `_set_user_requirements` 方法
```python
def _set_user_requirements(self, user_requirements: str = None, user_relationship_images: str = None, user_analysis: str = None):
    """设置用户需求并处理相关变量"""
    # ... 原有逻辑 ...
    
    # 处理案件分析需求
    if user_analysis and user_analysis.strip():
        self.user_analysis = user_analysis.strip()
    else:
        self.user_analysis = None
```

#### 修改 `_get_system_message` 方法 - 核心功能
```python
def _get_system_message(self) -> str:
    """生成系统消息，使用类的全局变量"""
    
    # 动态生成分析需求
    analysis_requirements = f"""
    【核心任务】
    1. 组织架构解析：识别人员、公司、组织间的关系和角色分工
    2. 供应链分析：梳理上下游关系、物流路径和交易链条
    3. 结构化提取案件要素（严格按照{self.user_requirements_count}列CSV格式）
    4. 生成多维度关系图谱（使用Mermaid语法，展现所有关联关系）
    
    第三步：关系图谱生成
    {self.user_relationship_images}
    """
    
    # 根据是否有用户自定义分析需求来生成不同的系统消息
    if self.user_analysis:
        # 使用用户自定义的案件分析需求
        return f"""{self.user_analysis}

{analysis_requirements}

返回格式：
{{
    "analysis": "详细的组织架构和供应链分析，包括：1)人员/公司/组织关系结构；2)人员角色分工；3)作案链条；4)关键节点分析",
    "csv_data": "严格按照{self.user_requirements_count}列格式的CSV数据，每个实体占一行",
    "mermaid_code": "完整的Mermaid关系图代码，确保语法正确、可直接渲染"
}}
"""
    else:
        # 使用默认的系统消息
        return f"""你是一位走私案件资深分析专家，专门负责分析走私案件起诉意见书，提取关键要素并生成可视化关系图谱。

{analysis_requirements}

返回格式：
{{
    "analysis": "详细的组织架构和供应链分析，包括：1)人员/公司/组织关系结构；2)人员角色分工；3)作案链条；4)关键节点分析",
    "csv_data": "严格按照{self.user_requirements_count}列格式的CSV数据，每个实体占一行",
    "mermaid_code": "完整的Mermaid关系图代码，确保语法正确、可直接渲染"
}}
"""
```

#### 修改方法签名
- `_initialize_agent(user_requirements, user_relationship_images, user_analysis)`
- `extract_multiple_cases(..., user_analysis, ...)`
- `process_multi_case_file(..., user_analysis)`

### 2. 前端修改 - streamlit_app.py

#### 新增案件分析输入框
```python
# 案件分析需求定义
st.markdown("### 🔍 案件分析")
user_analysis = st.text_area(
    "案件分析需求配置",
    value="",
    height=150,
    help="请输入案件分析的具体需求，留空则使用默认的走私案件分析专家角色",
    label_visibility="collapsed",
    placeholder="例如：你是一位金融犯罪分析专家，专门负责分析金融诈骗案件..."
)
```

#### 修改函数返回值
```python
# 修改前
return uploaded_file, user_requirements, user_relationship_images

# 修改后
return uploaded_file, user_requirements, user_relationship_images, user_analysis
```

#### 修改主函数调用
```python
# 修改前
uploaded_file, user_requirements, user_relationship_images = display_file_upload_section()

# 修改后
uploaded_file, user_requirements, user_relationship_images, user_analysis = display_file_upload_section()
```

#### 修改后端调用
```python
result = asyncio.run(st.session_state.orchestrator.process_multi_case_file(
    saved_file_path,
    st.session_state.current_session_id,
    user_requirements,
    st.session_state.max_concurrent,
    progress_callback,
    user_relationship_images,
    user_analysis  # 新增参数
))
```

## 🎨 界面设计

### 前端界面结构
```
### 📋 分析需求
┌─────────────────────────────────────────────────────────┐
│ 字段配置 (高度: 100px)                                  │
└─────────────────────────────────────────────────────────┘

### 🖼️ 案件人物关系图
┌─────────────────────────────────────────────────────────┐
│ 关系图需求配置 (高度: 200px)                            │
└─────────────────────────────────────────────────────────┘

### 🔍 案件分析
┌─────────────────────────────────────────────────────────┐
│ 案件分析需求配置 (高度: 150px)                          │
│ 占位符：例如：你是一位金融犯罪分析专家，专门负责分析金融│
│ 诈骗案件...                                            │
│ 帮助提示：请输入案件分析的具体需求，留空则使用默认的走私│
│ 案件分析专家角色                                        │
└─────────────────────────────────────────────────────────┘
```

### 设计特点
- ✅ **三个独立配置区域**：字段、关系图、案件分析分开
- ✅ **合适的高度**：案件分析配置150px
- ✅ **默认为空**：保持向后兼容性
- ✅ **清晰的提示**：占位符和帮助文本指导用户使用

## 🔄 数据流

### 完整数据传递链路
```
前端用户输入
    ↓
display_file_upload_section()
    ↓ (uploaded_file, user_requirements, user_relationship_images, user_analysis)
main()
    ↓
orchestrator.process_multi_case_file(..., user_analysis)
    ↓
batch_extractor.extract_multiple_cases(..., user_analysis)
    ↓
_initialize_agent(user_requirements, user_relationship_images, user_analysis)
    ↓
_set_user_requirements(user_requirements, user_relationship_images, user_analysis)
    ↓
self.user_analysis = user_analysis
    ↓
_get_system_message() → 动态角色判断
    ↓
if self.user_analysis: 使用用户自定义角色
else: 使用默认走私案件分析专家角色
```

## 🎯 核心逻辑

### 动态系统消息生成
```python
# 当用户输入案件分析内容时
if self.user_analysis:
    return f"""{self.user_analysis}  # 用户自定义的专家角色

{analysis_requirements}  # 分析任务和要求

返回格式：...
"""

# 当用户未输入案件分析内容时
else:
    return f"""你是一位走私案件资深分析专家，专门负责分析走私案件起诉意见书，提取关键要素并生成可视化关系图谱。

{analysis_requirements}  # 分析任务和要求

返回格式：...
"""
```

## 📊 功能对比

### 修改前
- **专家角色**：固定的走私案件分析专家
- **适用范围**：主要针对走私案件
- **灵活性**：角色描述不可变

### 修改后
- **专家角色**：用户可自定义任何类型的案件分析专家
- **适用范围**：支持各种类型案件（金融、诈骗、毒品等）
- **灵活性**：完全可定制的专家角色和分析方法
- **向后兼容**：留空时使用默认的走私案件专家

## 🎯 用户价值

### 1. 灵活性提升
- ✅ **多类型案件支持**：金融犯罪、诈骗案件、毒品案件等
- ✅ **专家角色定制**：用户可根据案件类型定制专业角色
- ✅ **分析方法适配**：不同专家有不同的分析方法和关注点

### 2. 专业性增强
- ✅ **领域专业性**：金融专家关注资金流向，毒品专家关注供应链
- ✅ **术语准确性**：不同领域使用相应的专业术语
- ✅ **分析深度**：专业角色能提供更深入的领域分析

### 3. 用户体验优化
- ✅ **简单易用**：留空即使用默认角色，降低使用门槛
- ✅ **灵活配置**：有需要时可以详细定制专家角色
- ✅ **即时生效**：角色配置立即应用到分析过程

## 🔧 技术实现

### 变量管理
- **全局一致性**：`self.user_analysis` 在整个类中保持一致
- **条件判断**：通过 `if self.user_analysis:` 实现动态切换
- **参数传递**：完整的参数传递链路确保配置正确传递

### 动态消息生成
- **条件分支**：根据是否有用户输入决定使用哪种角色
- **保持结构**：保持原有的分析任务和返回格式结构
- **向后兼容**：不影响现有的字段配置和关系图功能

### 前端集成
- **无缝集成**：新功能与现有界面完美融合
- **数据同步**：前后端数据传递完整可靠
- **错误处理**：保持原有的错误处理机制

## 🎉 总结

### 完成的功能
1. ✅ **新增全局变量**：`self.user_analysis`
2. ✅ **前端输入框**：专门的案件分析配置区域
3. ✅ **动态系统消息**：根据用户输入动态生成专家角色
4. ✅ **完整数据流**：从前端到后端的完整参数传递
5. ✅ **向后兼容**：默认为空，保持原有功能

### 用户体验提升
- **更灵活的角色定制**：用户可根据案件类型定制专家角色
- **更专业的分析**：不同领域的专家提供更专业的分析
- **更广泛的适用性**：支持各种类型的案件分析
- **更简单的使用**：留空即使用默认角色，有需要时可定制

现在用户可以在前端界面中自定义案件分析专家角色，系统会根据用户的配置动态生成相应的专家角色，提供更专业、更适合的案件分析服务！🎯
